import 'dart:async';

import 'package:flutter_audio_room/services/websocket_service/data/optimized_websocket_datasource.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

enum CloseCode {
  initial(0),
  normal(1),
  goingAway(1000),
  networkError(1005),
  ;

  const CloseCode(this.value);
  final int value;
}

/// WebSocket data source interface
abstract class WebSocketDataSource {
  /// 获取WebSocket URL
  String get url;

  /// 连接到WebSocket服务器
  Future<ResultWithData<void>> connect();

  /// 断开WebSocket连接
  Future<ResultWithData<void>> disconnect({required CloseCode closeCode});

  /// 消息流，用于接收服务器消息
  Stream<response.WebsocketResponse> get messageStream;

  /// 发送消息到WebSocket服务器
  Future<ResultWithData<void>> sendMessage(WebSocketMessageEntity message);

  /// 当前连接状态
  bool get isConnected;

  /// 开始心跳检测
  void startHeartbeat();

  /// 停止心跳检测
  void stopHeartbeat();

  /// 尝试刷新Token并重连
  Future<void> tryRefreshTokenAndReconnect();

  /// 重置重连状态，允许重新开始重连
  void resetReconnectionState();

  Future<void> dispose();
}

/// Legacy WebSocket data source implementation
///
/// @deprecated Use OptimizedWebSocketDataSource instead
/// This implementation is kept for backward compatibility
class WebSocketDataSourceImpl implements WebSocketDataSource {
  WebSocketDataSourceImpl({
    required StorageService storageService,
    required String url,
  }) {
    // Delegate to optimized implementation
    _optimizedDataSource = OptimizedWebSocketDataSource.create(
      storageService: storageService,
      url: url,
    );
  }

  late final OptimizedWebSocketDataSource _optimizedDataSource;

  @override
  String get url => _optimizedDataSource.url;

  @override
  bool get isConnected => _optimizedDataSource.isConnected;

  @override
  Stream<response.WebsocketResponse> get messageStream =>
      _optimizedDataSource.messageStream;

  @override
  Future<ResultWithData<void>> connect() async {
    return await _optimizedDataSource.connect();
  }

  @override
  Future<ResultWithData<void>> disconnect(
      {required CloseCode closeCode}) async {
    return await _optimizedDataSource.disconnect(closeCode: closeCode);
  }

  @override
  Future<ResultWithData<void>> sendMessage(
      WebSocketMessageEntity message) async {
    return await _optimizedDataSource.sendMessage(message);
  }

  @override
  void startHeartbeat() {
    _optimizedDataSource.startHeartbeat();
  }

  @override
  void stopHeartbeat() {
    _optimizedDataSource.stopHeartbeat();
  }

  @override
  Future<void> tryRefreshTokenAndReconnect() async {
    await _optimizedDataSource.tryRefreshTokenAndReconnect();
  }

  @override
  void resetReconnectionState() {
    _optimizedDataSource.resetReconnectionState();
  }

  @override
  Future<void> dispose() async {
    await _optimizedDataSource.dispose();
  }

}

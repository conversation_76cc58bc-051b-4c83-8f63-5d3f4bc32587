import 'dart:async';

import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart' as response;

/// Message processor
/// Handles message serialization, deserialization, and routing
class WebSocketMessageProcessor {
  final IWebSocketMessageSerializer _serializer;
  final IWebSocketLogger _logger;
  final void Function() _onHeartbeatResponse;

  final StreamController<response.WebsocketResponse> _messageController =
      StreamController<response.WebsocketResponse>.broadcast();

  WebSocketMessageProcessor({
    required IWebSocketMessageSerializer serializer,
    required IWebSocketLogger logger,
    required void Function() onHeartbeatResponse,
  })  : _serializer = serializer,
        _logger = logger,
        _onHeartbeatResponse = onHeartbeatResponse;

  /// Message stream for external consumers
  Stream<response.WebsocketResponse> get messageStream => _messageController.stream;

  /// Process incoming raw message
  void processIncomingMessage(String rawMessage) {
    try {
      _logger.debug('Processing incoming message: $rawMessage', 
          tag: 'MessageProcessor');

      // Check if it's a heartbeat response
      if (_serializer.isHeartbeatResponse(rawMessage)) {
        _onHeartbeatResponse();
        return;
      }

      // Deserialize and emit the message
      final message = _serializer.deserialize(rawMessage);
      _messageController.add(message);
      
      _logger.debug('Message processed and emitted', tag: 'MessageProcessor');
      
    } catch (e, stackTrace) {
      _logger.error(
        'Failed to process incoming message',
        error: e,
        stackTrace: stackTrace,
        tag: 'MessageProcessor',
      );
      
      // Add error to stream so consumers can handle it
      _messageController.addError(e, stackTrace);
    }
  }

  /// Serialize outgoing message
  String serializeOutgoingMessage(WebSocketMessageEntity message) {
    try {
      final serialized = _serializer.serialize(message);
      _logger.debug('Message serialized: $serialized', tag: 'MessageProcessor');
      return serialized;
    } catch (e) {
      _logger.error(
        'Failed to serialize outgoing message',
        error: e,
        tag: 'MessageProcessor',
      );
      rethrow;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    await _messageController.close();
    _logger.debug('Message processor disposed', tag: 'MessageProcessor');
  }
}

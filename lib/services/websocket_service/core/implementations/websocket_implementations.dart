import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/services/websocket_service/core/interfaces/websocket_interfaces.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

/// Default WebSocket configuration implementation
class DefaultWebSocketConfig implements IWebSocketConfig {
  final String _url;
  final Map<String, String> _headers;

  DefaultWebSocketConfig({
    required String url,
    Map<String, String>? headers,
  })  : _url = url,
        _headers = headers ?? {};

  @override
  String get url => _url;

  @override
  Map<String, String> get headers => Map.unmodifiable(_headers);

  @override
  int get heartbeatInterval => 30;

  @override
  int get initialReconnectDelay => 1;

  @override
  int get maxReconnectDelay => 15;

  @override
  int get maxReconnectAttempts => 0; // Unlimited

  @override
  int get connectionTimeout => 10;

  @override
  String get heartbeatMessage => jsonEncode({'msg': 'ping'});
}

/// Default WebSocket connection provider implementation
class DefaultWebSocketConnectionProvider
    implements IWebSocketConnectionProvider {
  @override
  Future<dynamic> connect(String url, Map<String, String> headers) async {
    return await WebSocket.connect(url, headers: headers);
  }
}

/// Default WebSocket message serializer implementation
class DefaultWebSocketMessageSerializer implements IWebSocketMessageSerializer {
  @override
  String serialize(WebSocketMessageEntity message) {
    return message.data;
  }

  @override
  response.WebsocketResponse deserialize(String data) {
    final json = jsonDecode(data);
    return response.WebsocketResponse.fromJson(json);
  }

  @override
  bool isHeartbeatResponse(String data) {
    try {
      final json = jsonDecode(data);
      return json['msg'] == 'pong';
    } catch (_) {
      return false;
    }
  }
}

/// Storage-based WebSocket authentication provider implementation
class StorageWebSocketAuthProvider implements IWebSocketAuthProvider {
  final StorageService _storageService;

  StorageWebSocketAuthProvider({
    required StorageService storageService,
  }) : _storageService = storageService;

  @override
  String get accessToken => _storageService.accessToken;

  @override
  String get timezone => _storageService.getString(SPKeys.timezone) ?? '';

  @override
  Future<ResultWithData<String>> refreshToken() async {
    final result = await getIt<TokenRefreshService>().refreshToken();
    return result.fold(
      (error) => Either.left(error),
      (token) => Either.right(token),
    );
  }
}

/// GetIt-based WebSocket device info provider implementation
class GetItWebSocketDeviceInfoProvider implements IWebSocketDeviceInfoProvider {
  @override
  String get platform => getIt<IDeviceInfoService>().platform;

  @override
  String get version => getIt<IPackageInfoService>().version;
}

/// Default WebSocket error handler implementation
class DefaultWebSocketErrorHandler implements IWebSocketErrorHandler {
  final IWebSocketLogger _logger;

  DefaultWebSocketErrorHandler({
    required IWebSocketLogger logger,
  }) : _logger = logger;

  @override
  void handleConnectionError(dynamic error, StackTrace? stackTrace) {
    _logger.error('Connection error occurred',
        error: error, stackTrace: stackTrace, tag: 'ErrorHandler');
  }

  @override
  void handleMessageError(dynamic error, StackTrace? stackTrace) {
    _logger.error('Message error occurred',
        error: error, stackTrace: stackTrace, tag: 'ErrorHandler');
  }

  @override
  void handleHeartbeatTimeout() {
    _logger.warning('Heartbeat timeout detected', tag: 'ErrorHandler');
  }

  @override
  bool shouldRetryConnection(dynamic error) {
    if (error is AppException) {
      // Don't retry for authentication errors
      if (error.identifier == 'WS_NO_SESSION_INFO') {
        return false;
      }
      // Don't retry for client errors (4xx)
      if (error.statusCode >= 400 && error.statusCode < 500) {
        return false;
      }
    }
    return true;
  }
}

/// LogUtils-based WebSocket logger implementation
class LogUtilsWebSocketLogger implements IWebSocketLogger {
  @override
  void debug(String message, {String? tag}) {
    LogUtils.d(message, tag: tag ?? 'WebSocket');
  }

  @override
  void info(String message, {String? tag}) {
    LogUtils.i(message, tag: tag ?? 'WebSocket');
  }

  @override
  void warning(String message, {String? tag}) {
    LogUtils.w(message, tag: tag ?? 'WebSocket');
  }

  @override
  void error(String message,
      {dynamic error, StackTrace? stackTrace, String? tag}) {
    LogUtils.e(message,
        error: error, stackTrace: stackTrace, tag: tag ?? 'WebSocket');
  }
}

/// Enhanced WebSocket configuration with custom headers
class EnhancedWebSocketConfig extends DefaultWebSocketConfig {
  final IWebSocketAuthProvider _authProvider;
  final IWebSocketDeviceInfoProvider _deviceInfoProvider;

  EnhancedWebSocketConfig({
    required super.url,
    required IWebSocketAuthProvider authProvider,
    required IWebSocketDeviceInfoProvider deviceInfoProvider,
    Map<String, String>? additionalHeaders,
  })  : _authProvider = authProvider,
        _deviceInfoProvider = deviceInfoProvider,
        super(headers: additionalHeaders);

  @override
  Map<String, String> get headers {
    final baseHeaders = super.headers;
    final authHeaders = {
      'accept': 'application/json',
      'content-type': 'application/json',
      'GK-platform': _deviceInfoProvider.platform,
      'GK-app-version': _deviceInfoProvider.version,
      'GK-Timezone': _authProvider.timezone,
      'Authorization': 'Bearer ${_authProvider.accessToken}',
    };

    return {...baseHeaders, ...authHeaders};
  }
}

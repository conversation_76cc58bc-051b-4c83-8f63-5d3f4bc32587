import 'dart:convert';

import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:socket_io_client/socket_io_client.dart';

// Socket.IO data source implementation
class SocketIODataSource {
  Socket? _socket;

  // Get common socket headers
  Map<String, String> _getSocketHeaders({String? newToken}) {
    final accessToken = getIt<StorageService>().accessToken;
    final timezone =
        getIt<StorageService>().getString(SPKeys.timezone) ?? 'UTC';

    if (accessToken.isEmpty) {
      throw const AppException(
        message: 'Access token is empty',
        statusCode: 401,
        identifier: 'SocketIODataSource',
      );
    }

    return {
      'Authorization': 'Bearer ${newToken ?? accessToken}',
      'GK-Timezone': timezone,
      'GK-platform': getIt<IDeviceInfoService>().platform,
      'GK-DeviceId': getIt<IDeviceInfoService>().deviceId,
      'GK-app-version': getIt<IPackageInfoService>().version,
    };
  }

  // Initialize socket connection
  void initializeSocket(String url) {
    try {
      if (_socket != null) {
        _socket!.disconnect();
      }

      final headers = _getSocketHeaders();

      _socket = io(
        url,
        OptionBuilder()
            .setTransports(['websocket'])
            .setExtraHeaders(headers)
            .build(),
      );
    } catch (e) {
      LogUtils.e('Failed to initialize socket: $e', tag: 'SocketIODataSource');
    }
  }

  // Setup basic socket event listeners
  void onConnect(dynamic Function(dynamic) handler) {
    _socket?.onConnect((data) {
      try {
        handler(data);
      } catch (e) {
        LogUtils.e('Error in connect handler: $e', tag: 'SocketIODataSource');
      }
    });
  }

  void onReconnect(dynamic Function(dynamic) handler) {
    _socket?.onReconnect((data) {
      try {
        handler(data);
      } catch (e) {
        LogUtils.e('Error in reconnect handler: $e', tag: 'SocketIODataSource');
      }
    });
  }

  void onConnectError(dynamic Function(dynamic) handler) {
    _socket?.onConnectError((error) {
      try {
        handler(error);
      } catch (e) {
        LogUtils.e('Error in connectError handler: $e',
            tag: 'SocketIODataSource');
      }
    });
  }

  void onDisconnect(dynamic Function(dynamic) handler) {
    _socket?.onDisconnect((data) {
      try {
        handler(data);
      } catch (e) {
        LogUtils.e('Error in disconnect handler: $e',
            tag: 'SocketIODataSource');
      }
    });
  }

  void onError(dynamic Function(dynamic) handler) {
    _socket?.onError((error) {
      try {
        handler(error);
      } catch (e) {
        LogUtils.e('Error in error handler: $e', tag: 'SocketIODataSource');
      }
    });
  }

  void emitWithAck(
      String event, Map<String, dynamic> data, Function(dynamic) ack) {
    return _socket?.emitWithAck(event, jsonEncode(data), ack: (res) {
      LogUtils.d('Ack received: $res', tag: 'SocketIODataSource');

      try {
        ack(res);
      } catch (e) {
        LogUtils.e('Error in ack callback: $e', tag: 'SocketIODataSource');
      }
    });
  }

  // Emit event with data
  void emit(String event, Map<String, dynamic> data) {
    _socket?.emit(event, jsonEncode(data));
  }

  // Listen to specific event
  void on(String event, Function(dynamic) handler) {
    _socket?.on(event, (data) {
      try {
        handler(data);
      } catch (e) {
        LogUtils.e('Error in event handler for $event: $e',
            tag: 'SocketIODataSource');
      }
    });
  }

  // Remove event listener
  void off(String event) {
    _socket?.off(event);
  }

  void connect() {
    _socket?.connect();
  }

  // Disconnect socket
  void disconnect() {
    _socket?.disconnect();
  }

  void dispose() {
    _socket?.dispose();
    _socket = null;
    LogUtils.d('socketIODataSource disposed', tag: 'SocketIODataSource');
  }

  // Check if socket is connected
  bool get isConnected => _socket?.connected ?? false;

  // Reconnect socket with updated headers
  void reconnectWithNewToken(String newToken) {
    EasyThrottle.throttle(
      'reconnectWithNewToken',
      const Duration(seconds: 2),
      () {
        try {
          final headers = _getSocketHeaders(newToken: newToken);
          LogUtils.d('Updating socket headers with new token',
              tag: 'SocketIODataSource');

          if (_socket != null) {
            // Update headers and reconnect
            _socket!.io.options?['extraHeaders'] = headers;
            _socket!.io
              ..disconnect()
              ..connect();
          }
        } catch (e) {
          LogUtils.e('Failed to reconnect socket: $e',
              tag: 'SocketIODataSource');
        }
      },
    );
  }
}
